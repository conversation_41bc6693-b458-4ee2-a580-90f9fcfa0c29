/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// IAMCredentialSpec defines the desired state of IAMCredential
type IAMCredentialSpec struct {
	// Source defines where to find the IAM credentials
	// +required
	Source IAMCredentialSource `json:"source"`

	// Validation defines validation behavior
	// +optional
	Validation *IAMCredentialValidation `json:"validation,omitempty"`

	// Schedule defines periodic validation schedule (e.g., "*/5m" for every 5 minutes)
	// +optional
	Schedule *string `json:"schedule,omitempty"`
}

// IAMCredentialSource defines the source of IAM credentials
type IAMCredentialSource struct {
	// ConfigMapRef references a ConfigMap containing credentials
	// +optional
	ConfigMapRef *ConfigMapReference `json:"configMapRef,omitempty"`

	// SecretRef references a Secret containing credentials
	// +optional
	SecretRef *SecretReference `json:"secretRef,omitempty"`

	// Inline credentials (for testing only, not recommended for production)
	// +optional
	Inline *InlineCredentials `json:"inline,omitempty"`
}

// ConfigMapReference references a ConfigMap
type ConfigMapReference struct {
	// Name of the ConfigMap
	// +required
	Name string `json:"name"`

	// Namespace of the ConfigMap (defaults to the same namespace as IAMCredential)
	// +optional
	Namespace string `json:"namespace,omitempty"`

	// Key within the ConfigMap (optional, will scan all keys if not specified)
	// +optional
	Key string `json:"key,omitempty"`
}

// SecretReference references a Secret
type SecretReference struct {
	// Name of the Secret
	// +required
	Name string `json:"name"`

	// Namespace of the Secret (defaults to the same namespace as IAMCredential)
	// +optional
	Namespace string `json:"namespace,omitempty"`

	// Key within the Secret (optional, will scan all keys if not specified)
	// +optional
	Key string `json:"key,omitempty"`
}

// InlineCredentials defines inline credentials (for testing only)
type InlineCredentials struct {
	// AccessKey for IAM authentication
	// +required
	AccessKey string `json:"accessKey"`

	// SecretKey for IAM authentication
	// +required
	SecretKey string `json:"secretKey"`

	// Additional credential data
	// +optional
	Data map[string]string `json:"data,omitempty"`
}

// IAMCredentialValidation defines validation behavior
type IAMCredentialValidation struct {
	// Enabled controls whether validation is performed
	// +optional
	// +kubebuilder:default=true
	Enabled *bool `json:"enabled,omitempty"`

	// Endpoint specifies the IAM API endpoint ("auto-discover" for automatic discovery)
	// +optional
	// +kubebuilder:default="auto-discover"
	Endpoint string `json:"endpoint,omitempty"`

	// Timeout for validation requests
	// +optional
	// +kubebuilder:default="30s"
	Timeout *metav1.Duration `json:"timeout,omitempty"`
}

// IAMCredentialStatus defines the observed state of IAMCredential.
type IAMCredentialStatus struct {
	// Phase represents the current phase of the IAMCredential
	// +optional
	Phase IAMCredentialPhase `json:"phase,omitempty"`

	// Conditions represent the latest available observations of the IAMCredential's state
	// +optional
	Conditions []metav1.Condition `json:"conditions,omitempty"`

	// LastValidated timestamp of last successful validation
	// +optional
	LastValidated *metav1.Time `json:"lastValidated,omitempty"`

	// ValidationResult contains the result of the last validation
	// +optional
	ValidationResult *ValidationResult `json:"validationResult,omitempty"`

	// ObservedGeneration reflects the generation of the most recently observed IAMCredential
	// +optional
	ObservedGeneration int64 `json:"observedGeneration,omitempty"`
}

// IAMCredentialPhase represents the current phase of an IAMCredential
// +kubebuilder:validation:Enum=Pending;Validating;Valid;Invalid;Failed
type IAMCredentialPhase string

const (
	// IAMCredentialPhasePending indicates the credential is pending processing
	IAMCredentialPhasePending IAMCredentialPhase = "Pending"
	// IAMCredentialPhaseValidating indicates the credential is being validated
	IAMCredentialPhaseValidating IAMCredentialPhase = "Validating"
	// IAMCredentialPhaseValid indicates the credential is valid
	IAMCredentialPhaseValid IAMCredentialPhase = "Valid"
	// IAMCredentialPhaseInvalid indicates the credential is invalid
	IAMCredentialPhaseInvalid IAMCredentialPhase = "Invalid"
	// IAMCredentialPhaseFailed indicates validation failed due to an error
	IAMCredentialPhaseFailed IAMCredentialPhase = "Failed"
)

// ValidationResult contains the result of credential validation
type ValidationResult struct {
	// Success indicates whether validation was successful
	// +required
	Success bool `json:"success"`

	// Message provides additional information about the validation result
	// +optional
	Message string `json:"message,omitempty"`

	// Endpoint is the IAM API endpoint that was used for validation
	// +optional
	Endpoint string `json:"endpoint,omitempty"`

	// AccessKeyPrefix shows the first 8 characters of the access key (for security)
	// +optional
	AccessKeyPrefix string `json:"accessKeyPrefix,omitempty"`

	// ValidationTime is when the validation was performed
	// +optional
	ValidationTime *metav1.Time `json:"validationTime,omitempty"`

	// Error contains error details if validation failed
	// +optional
	Error string `json:"error,omitempty"`
}

// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
// +kubebuilder:resource:scope=Namespaced,shortName=iamcred
// +kubebuilder:printcolumn:name="Phase",type="string",JSONPath=".status.phase"
// +kubebuilder:printcolumn:name="Last Validated",type="date",JSONPath=".status.lastValidated"
// +kubebuilder:printcolumn:name="Source",type="string",JSONPath=".spec.source.configMapRef.name"
// +kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"

// IAMCredential is the Schema for the iamcredentials API
type IAMCredential struct {
	metav1.TypeMeta `json:",inline"`

	// metadata is a standard object metadata
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty,omitzero"`

	// spec defines the desired state of IAMCredential
	// +required
	Spec IAMCredentialSpec `json:"spec"`

	// status defines the observed state of IAMCredential
	// +optional
	Status IAMCredentialStatus `json:"status,omitempty,omitzero"`
}

// +kubebuilder:object:root=true

// IAMCredentialList contains a list of IAMCredential
type IAMCredentialList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []IAMCredential `json:"items"`
}

func init() {
	SchemeBuilder.Register(&IAMCredential{}, &IAMCredentialList{})
}
