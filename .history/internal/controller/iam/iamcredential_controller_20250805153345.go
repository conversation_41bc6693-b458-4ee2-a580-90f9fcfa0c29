/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package iam

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	iamv1 "github.com/abc-stack/iam-operator/api/iam/v1"
	"github.com/abc-stack/iam-operator/internal/config"
	"github.com/abc-stack/iam-operator/internal/discovery"
	"github.com/abc-stack/iam-operator/internal/processors"
)

// IAMCredentialReconciler reconciles a IAMCredential object
type IAMCredentialReconciler struct {
	client.Client
	Scheme *runtime.Scheme

	// Dependencies
	Config              *config.Config
	ServiceDiscovery    *discovery.ServiceDiscovery
	CredentialProcessor *processors.CredentialProcessor
	KubernetesClient    kubernetes.Interface
}

// +kubebuilder:rbac:groups=iam.abcstackint.com,resources=iamcredentials,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=iam.abcstackint.com,resources=iamcredentials/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=iam.abcstackint.com,resources=iamcredentials/finalizers,verbs=update
// +kubebuilder:rbac:groups="",resources=configmaps,verbs=get;list;watch
// +kubebuilder:rbac:groups="",resources=secrets,verbs=get;list;watch

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
func (r *IAMCredentialReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// Fetch the IAMCredential instance
	var iamCred iamv1.IAMCredential
	if err := r.Get(ctx, req.NamespacedName, &iamCred); err != nil {
		if errors.IsNotFound(err) {
			// Request object not found, could have been deleted after reconcile request.
			logger.Info("IAMCredential resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		// Error reading the object - requeue the request.
		logger.Error(err, "Failed to get IAMCredential")
		return ctrl.Result{}, err
	}

	logger.Info("Reconciling IAMCredential", "name", iamCred.Name, "namespace", iamCred.Namespace)

	// Update observed generation
	if iamCred.Status.ObservedGeneration != iamCred.Generation {
		iamCred.Status.ObservedGeneration = iamCred.Generation
	}

	// Set initial phase if not set
	if iamCred.Status.Phase == "" {
		iamCred.Status.Phase = iamv1.IAMCredentialPhasePending
		if err := r.updateStatus(ctx, &iamCred); err != nil {
			return ctrl.Result{}, err
		}
	}

	// Process the credential based on current phase
	switch iamCred.Status.Phase {
	case iamv1.IAMCredentialPhasePending:
		return r.handlePendingPhase(ctx, &iamCred)
	case iamv1.IAMCredentialPhaseValidating:
		return r.handleValidatingPhase(ctx, &iamCred)
	case iamv1.IAMCredentialPhaseValid:
		return r.handleValidPhase(ctx, &iamCred)
	case iamv1.IAMCredentialPhaseInvalid, iamv1.IAMCredentialPhaseFailed:
		return r.handleFailedPhase(ctx, &iamCred)
	default:
		logger.Info("Unknown phase, resetting to Pending", "phase", iamCred.Status.Phase)
		iamCred.Status.Phase = iamv1.IAMCredentialPhasePending
		return ctrl.Result{Requeue: true}, r.updateStatus(ctx, &iamCred)
	}
}

// updateStatus updates the status of the IAMCredential resource
func (r *IAMCredentialReconciler) updateStatus(ctx context.Context, iamCred *iamv1.IAMCredential) error {
	return r.Status().Update(ctx, iamCred)
}

// handlePendingPhase handles the Pending phase
func (r *IAMCredentialReconciler) handlePendingPhase(ctx context.Context, iamCred *iamv1.IAMCredential) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	logger.Info("Handling Pending phase", "name", iamCred.Name)

	// Transition to Validating phase
	iamCred.Status.Phase = iamv1.IAMCredentialPhaseValidating
	if err := r.updateStatus(ctx, iamCred); err != nil {
		return ctrl.Result{}, err
	}

	// Requeue immediately to start validation
	return ctrl.Result{Requeue: true}, nil
}

// handleValidatingPhase handles the Validating phase
func (r *IAMCredentialReconciler) handleValidatingPhase(ctx context.Context, iamCred *iamv1.IAMCredential) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	logger.Info("Handling Validating phase", "name", iamCred.Name)

	// Extract credentials from the specified source
	credentials, err := r.extractCredentialsFromSource(ctx, iamCred)
	if err != nil {
		return r.transitionToFailed(ctx, iamCred, fmt.Sprintf("Failed to extract credentials: %v", err))
	}

	if len(credentials) == 0 {
		return r.transitionToFailed(ctx, iamCred, "No credentials found in source")
	}

	// Validate credentials using the existing processor
	validationSuccess := true
	var validationError error
	var endpoint string

	// Get IAM endpoint
	if r.ServiceDiscovery != nil {
		endpoint, err = r.ServiceDiscovery.GetIAMEndpoint(ctx)
		if err != nil {
			logger.Error(err, "Failed to discover IAM endpoint")
			endpoint = "unknown"
		}
	}

	// Process each credential
	for productName, cred := range credentials {
		if err := r.CredentialProcessor.ProcessCredentials(ctx, "IAMCredential", iamCred.Name, map[string]string{
			"access_key": cred.AccessKey,
			"secret_key": cred.SecretKey,
		}); err != nil {
			logger.Error(err, "Credential validation failed", "product", productName)
			validationSuccess = false
			validationError = err
			break
		}
	}

	// Update status based on validation result
	now := metav1.Now()
	iamCred.Status.LastValidated = &now

	if validationSuccess {
		return r.transitionToValid(ctx, iamCred, endpoint)
	} else {
		return r.transitionToInvalid(ctx, iamCred, fmt.Sprintf("Validation failed: %v", validationError))
	}
}

// handleValidPhase handles the Valid phase
func (r *IAMCredentialReconciler) handleValidPhase(ctx context.Context, iamCred *iamv1.IAMCredential) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	logger.Info("Handling Valid phase", "name", iamCred.Name)

	// Check if we need to revalidate based on schedule
	if iamCred.Spec.Schedule != nil && *iamCred.Spec.Schedule != "" {
		// For now, implement a simple time-based revalidation
		// In a full implementation, you would parse the schedule and calculate next run time
		if iamCred.Status.LastValidated != nil {
			// Revalidate every 5 minutes as a simple example
			if time.Since(iamCred.Status.LastValidated.Time) > 5*time.Minute {
				logger.Info("Scheduled revalidation triggered")
				iamCred.Status.Phase = iamv1.IAMCredentialPhaseValidating
				if err := r.updateStatus(ctx, iamCred); err != nil {
					return ctrl.Result{}, err
				}
				return ctrl.Result{Requeue: true}, nil
			}
		}
	}

	// Requeue after a reasonable interval to check for schedule
	return ctrl.Result{RequeueAfter: 1 * time.Minute}, nil
}

// handleFailedPhase handles the Invalid and Failed phases
func (r *IAMCredentialReconciler) handleFailedPhase(ctx context.Context, iamCred *iamv1.IAMCredential) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	logger.Info("Handling Failed/Invalid phase", "name", iamCred.Name, "phase", iamCred.Status.Phase)

	// For failed credentials, we can implement retry logic here
	// For now, just requeue after a longer interval
	return ctrl.Result{RequeueAfter: 5 * time.Minute}, nil
}

// extractCredentialsFromSource extracts credentials from the specified source
func (r *IAMCredentialReconciler) extractCredentialsFromSource(ctx context.Context, iamCred *iamv1.IAMCredential) (map[string]*processors.Credential, error) {
	logger := log.FromContext(ctx)
	credentials := make(map[string]*processors.Credential)

	source := iamCred.Spec.Source

	// Handle ConfigMap source
	if source.ConfigMapRef != nil {
		configMapData, err := r.getConfigMapData(ctx, source.ConfigMapRef, iamCred.Namespace)
		if err != nil {
			return nil, fmt.Errorf("failed to get ConfigMap data: %w", err)
		}

		// Extract credentials using reflection to access private method
		// This is a temporary solution - ideally we'd expose a public method
		extractedCreds := r.extractCredentialsFromData(configMapData)
		for product, cred := range extractedCreds {
			credentials[product] = cred
		}
	}

	// Handle Secret source
	if source.SecretRef != nil {
		secretData, err := r.getSecretData(ctx, source.SecretRef, iamCred.Namespace)
		if err != nil {
			return nil, fmt.Errorf("failed to get Secret data: %w", err)
		}

		// Convert secret data to string map and extract credentials
		stringData := make(map[string]string)
		for key, value := range secretData {
			stringData[key] = string(value)
		}

		extractedCreds := r.extractCredentialsFromData(stringData)
		for product, cred := range extractedCreds {
			credentials[product] = cred
		}
	}

	// Handle inline source
	if source.Inline != nil {
		credentials["inline"] = &processors.Credential{
			AccessKey: source.Inline.AccessKey,
			SecretKey: source.Inline.SecretKey,
			Product:   "inline",
		}
	}

	logger.Info("Extracted credentials", "count", len(credentials))
	return credentials, nil
}

// getConfigMapData retrieves data from a ConfigMap
func (r *IAMCredentialReconciler) getConfigMapData(ctx context.Context, ref *iamv1.ConfigMapReference, defaultNamespace string) (map[string]string, error) {
	namespace := ref.Namespace
	if namespace == "" {
		namespace = defaultNamespace
	}

	var configMap corev1.ConfigMap
	if err := r.Get(ctx, types.NamespacedName{
		Name:      ref.Name,
		Namespace: namespace,
	}, &configMap); err != nil {
		return nil, err
	}

	// If a specific key is requested, return only that key
	if ref.Key != "" {
		if value, exists := configMap.Data[ref.Key]; exists {
			return map[string]string{ref.Key: value}, nil
		}
		return nil, fmt.Errorf("key %s not found in ConfigMap", ref.Key)
	}

	return configMap.Data, nil
}

// getSecretData retrieves data from a Secret
func (r *IAMCredentialReconciler) getSecretData(ctx context.Context, ref *iamv1.SecretReference, defaultNamespace string) (map[string][]byte, error) {
	namespace := ref.Namespace
	if namespace == "" {
		namespace = defaultNamespace
	}

	var secret corev1.Secret
	if err := r.Get(ctx, types.NamespacedName{
		Name:      ref.Name,
		Namespace: namespace,
	}, &secret); err != nil {
		return nil, err
	}

	// If a specific key is requested, return only that key
	if ref.Key != "" {
		if value, exists := secret.Data[ref.Key]; exists {
			return map[string][]byte{ref.Key: value}, nil
		}
		return nil, fmt.Errorf("key %s not found in Secret", ref.Key)
	}

	return secret.Data, nil
}

// transitionToValid transitions the IAMCredential to Valid phase
func (r *IAMCredentialReconciler) transitionToValid(ctx context.Context, iamCred *iamv1.IAMCredential, endpoint string) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	logger.Info("Transitioning to Valid phase", "name", iamCred.Name)

	iamCred.Status.Phase = iamv1.IAMCredentialPhaseValid

	// Update validation result
	now := metav1.Now()
	iamCred.Status.ValidationResult = &iamv1.ValidationResult{
		Success:        true,
		Message:        "Credential validation successful",
		Endpoint:       endpoint,
		ValidationTime: &now,
	}

	// Update conditions
	r.setCondition(iamCred, "Ready", metav1.ConditionTrue, "ValidationSuccessful", "IAM credentials validated successfully")

	if err := r.updateStatus(ctx, iamCred); err != nil {
		return ctrl.Result{}, err
	}

	// Requeue for periodic validation if schedule is set
	if iamCred.Spec.Schedule != nil && *iamCred.Spec.Schedule != "" {
		return ctrl.Result{RequeueAfter: 1 * time.Minute}, nil
	}

	return ctrl.Result{}, nil
}

// transitionToInvalid transitions the IAMCredential to Invalid phase
func (r *IAMCredentialReconciler) transitionToInvalid(ctx context.Context, iamCred *iamv1.IAMCredential, message string) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	logger.Info("Transitioning to Invalid phase", "name", iamCred.Name, "message", message)

	iamCred.Status.Phase = iamv1.IAMCredentialPhaseInvalid

	// Update validation result
	now := metav1.Now()
	iamCred.Status.ValidationResult = &iamv1.ValidationResult{
		Success:        false,
		Message:        message,
		ValidationTime: &now,
		Error:          message,
	}

	// Update conditions
	r.setCondition(iamCred, "Ready", metav1.ConditionFalse, "ValidationFailed", message)

	if err := r.updateStatus(ctx, iamCred); err != nil {
		return ctrl.Result{}, err
	}

	// Requeue after a longer interval for retry
	return ctrl.Result{RequeueAfter: 5 * time.Minute}, nil
}

// transitionToFailed transitions the IAMCredential to Failed phase
func (r *IAMCredentialReconciler) transitionToFailed(ctx context.Context, iamCred *iamv1.IAMCredential, message string) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	logger.Info("Transitioning to Failed phase", "name", iamCred.Name, "message", message)

	iamCred.Status.Phase = iamv1.IAMCredentialPhaseFailed

	// Update validation result
	now := metav1.Now()
	iamCred.Status.ValidationResult = &iamv1.ValidationResult{
		Success:        false,
		Message:        message,
		ValidationTime: &now,
		Error:          message,
	}

	// Update conditions
	r.setCondition(iamCred, "Ready", metav1.ConditionFalse, "ProcessingFailed", message)

	if err := r.updateStatus(ctx, iamCred); err != nil {
		return ctrl.Result{}, err
	}

	// Requeue after a longer interval for retry
	return ctrl.Result{RequeueAfter: 10 * time.Minute}, nil
}

// setCondition sets a condition on the IAMCredential status
func (r *IAMCredentialReconciler) setCondition(iamCred *iamv1.IAMCredential, conditionType string, status metav1.ConditionStatus, reason, message string) {
	condition := metav1.Condition{
		Type:               conditionType,
		Status:             status,
		Reason:             reason,
		Message:            message,
		LastTransitionTime: metav1.Now(),
	}

	// Find existing condition and update it, or append new one
	for i, existingCondition := range iamCred.Status.Conditions {
		if existingCondition.Type == conditionType {
			if existingCondition.Status != status {
				condition.LastTransitionTime = metav1.Now()
			} else {
				condition.LastTransitionTime = existingCondition.LastTransitionTime
			}
			iamCred.Status.Conditions[i] = condition
			return
		}
	}

	// Condition not found, append new one
	iamCred.Status.Conditions = append(iamCred.Status.Conditions, condition)
}

// extractCredentialsFromData extracts credentials from data map
// This duplicates some logic from CredentialProcessor to avoid accessing private methods
func (r *IAMCredentialReconciler) extractCredentialsFromData(data map[string]string) map[string]*processors.Credential {
	credentials := make(map[string]*processors.Credential)

	// Method 1: Extract direct key-value pairs (highest priority)
	directCreds := r.extractDirectCredentials(data)
	for product, cred := range directCreds {
		credentials[product] = cred
	}

	// Method 2: Extract from PHP config files
	phpCreds := r.extractPHPCredentials(data)
	for product, cred := range phpCreds {
		if _, exists := credentials[product]; !exists {
			credentials[product] = cred
		}
	}

	// Method 3: Extract from YAML/JSON nested configs
	yamlCreds := r.extractYAMLCredentials(data)
	for product, cred := range yamlCreds {
		if _, exists := credentials[product]; !exists {
			credentials[product] = cred
		}
	}

	// Method 4: Extract from config file format
	configCreds := r.extractConfigCredentials(data)
	for product, cred := range configCreds {
		if _, exists := credentials[product]; !exists {
			credentials[product] = cred
		}
	}

	return credentials
}

// extractDirectCredentials extracts direct key-value pairs
func (r *IAMCredentialReconciler) extractDirectCredentials(data map[string]string) map[string]*processors.Credential {
	credentials := make(map[string]*processors.Credential)

	// Common key patterns for access key and secret key
	accessKeyPatterns := []string{"access_key", "access-key", "accesskey", "ak"}
	secretKeyPatterns := []string{"secret_key", "secret-key", "secretkey", "sk"}

	var accessKey, secretKey string

	// Find access key
	for _, pattern := range accessKeyPatterns {
		if value, exists := data[pattern]; exists && value != "" {
			accessKey = value
			break
		}
	}

	// Find secret key
	for _, pattern := range secretKeyPatterns {
		if value, exists := data[pattern]; exists && value != "" {
			secretKey = value
			break
		}
	}

	if accessKey != "" && secretKey != "" {
		credentials["direct"] = &processors.Credential{
			AccessKey: accessKey,
			SecretKey: secretKey,
			Product:   "direct",
		}
	}

	return credentials
}

// extractPHPCredentials extracts credentials from PHP config format
func (r *IAMCredentialReconciler) extractPHPCredentials(data map[string]string) map[string]*processors.Credential {
	credentials := make(map[string]*processors.Credential)

	// PHP variable patterns
	phpPattern := regexp.MustCompile(`\$(\w+)_ak\s*=\s*['"]([^'"]+)['"]`)
	phpSecretPattern := regexp.MustCompile(`\$(\w+)_sk\s*=\s*['"]([^'"]+)['"]`)

	productKeys := make(map[string]string)
	productSecrets := make(map[string]string)

	for _, content := range data {
		// Find access keys
		matches := phpPattern.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) >= 3 {
				product := match[1]
				accessKey := match[2]
				productKeys[product] = accessKey
			}
		}

		// Find secret keys
		matches = phpSecretPattern.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) >= 3 {
				product := match[1]
				secretKey := match[2]
				productSecrets[product] = secretKey
			}
		}
	}

	// Match access keys with secret keys
	for product, accessKey := range productKeys {
		if secretKey, exists := productSecrets[product]; exists {
			credentials[product] = &processors.Credential{
				AccessKey: accessKey,
				SecretKey: secretKey,
				Product:   product,
			}
		}
	}

	return credentials
}

// extractYAMLCredentials extracts credentials from YAML/JSON nested format
func (r *IAMCredentialReconciler) extractYAMLCredentials(data map[string]string) map[string]*processors.Credential {
	credentials := make(map[string]*processors.Credential)

	// YAML nested patterns like iam.bss.ak, iam.bss.sk
	yamlPattern := regexp.MustCompile(`iam\.(\w+)\.ak:\s*['"]?([^'"]+)['"]?`)
	yamlSecretPattern := regexp.MustCompile(`iam\.(\w+)\.sk:\s*['"]?([^'"]+)['"]?`)

	productKeys := make(map[string]string)
	productSecrets := make(map[string]string)

	for _, content := range data {
		// Find access keys
		matches := yamlPattern.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) >= 3 {
				product := match[1]
				accessKey := strings.TrimSpace(match[2])
				productKeys[product] = accessKey
			}
		}

		// Find secret keys
		matches = yamlSecretPattern.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) >= 3 {
				product := match[1]
				secretKey := strings.TrimSpace(match[2])
				productSecrets[product] = secretKey
			}
		}
	}

	// Match access keys with secret keys
	for product, accessKey := range productKeys {
		if secretKey, exists := productSecrets[product]; exists {
			credentials[product] = &processors.Credential{
				AccessKey: accessKey,
				SecretKey: secretKey,
				Product:   product,
			}
		}
	}

	return credentials
}

// extractConfigCredentials extracts credentials from config file format
func (r *IAMCredentialReconciler) extractConfigCredentials(data map[string]string) map[string]*processors.Credential {
	credentials := make(map[string]*processors.Credential)

	// Config file patterns like bss_ak=xxx, bss_sk=yyy
	configPattern := regexp.MustCompile(`(\w+)_ak\s*=\s*([^\s]+)`)
	configSecretPattern := regexp.MustCompile(`(\w+)_sk\s*=\s*([^\s]+)`)

	productKeys := make(map[string]string)
	productSecrets := make(map[string]string)

	for _, content := range data {
		// Find access keys
		matches := configPattern.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) >= 3 {
				product := match[1]
				accessKey := match[2]
				productKeys[product] = accessKey
			}
		}

		// Find secret keys
		matches = configSecretPattern.FindAllStringSubmatch(content, -1)
		for _, match := range matches {
			if len(match) >= 3 {
				product := match[1]
				secretKey := match[2]
				productSecrets[product] = secretKey
			}
		}
	}

	// Match access keys with secret keys
	for product, accessKey := range productKeys {
		if secretKey, exists := productSecrets[product]; exists {
			credentials[product] = &processors.Credential{
				AccessKey: accessKey,
				SecretKey: secretKey,
				Product:   product,
			}
		}
	}

	return credentials
}

// SetupWithManager sets up the controller with the Manager.
func (r *IAMCredentialReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&iamv1.IAMCredential{}).
		Named("iam-iamcredential").
		Complete(r)
}
