/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package iam

import (
	"context"

	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	iamv1 "github.com/abc-stack/iam-operator/api/iam/v1"
	"github.com/abc-stack/iam-operator/internal/config"
	"github.com/abc-stack/iam-operator/internal/discovery"
	"github.com/abc-stack/iam-operator/internal/processors"
)

// IAMCredentialReconciler reconciles a IAMCredential object
type IAMCredentialReconciler struct {
	client.Client
	Scheme *runtime.Scheme

	// Dependencies
	Config              *config.Config
	ServiceDiscovery    *discovery.ServiceDiscovery
	CredentialProcessor *processors.CredentialProcessor
	KubernetesClient    kubernetes.Interface
}

// +kubebuilder:rbac:groups=iam.abcstackint.com,resources=iamcredentials,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=iam.abcstackint.com,resources=iamcredentials/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=iam.abcstackint.com,resources=iamcredentials/finalizers,verbs=update

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
func (r *IAMCredentialReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// Fetch the IAMCredential instance
	var iamCred iamv1.IAMCredential
	if err := r.Get(ctx, req.NamespacedName, &iamCred); err != nil {
		if errors.IsNotFound(err) {
			// Request object not found, could have been deleted after reconcile request.
			logger.Info("IAMCredential resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		// Error reading the object - requeue the request.
		logger.Error(err, "Failed to get IAMCredential")
		return ctrl.Result{}, err
	}

	logger.Info("Reconciling IAMCredential", "name", iamCred.Name, "namespace", iamCred.Namespace)

	// Update observed generation
	if iamCred.Status.ObservedGeneration != iamCred.Generation {
		iamCred.Status.ObservedGeneration = iamCred.Generation
	}

	// Set initial phase if not set
	if iamCred.Status.Phase == "" {
		iamCred.Status.Phase = iamv1.IAMCredentialPhasePending
		if err := r.updateStatus(ctx, &iamCred); err != nil {
			return ctrl.Result{}, err
		}
	}

	// Process the credential based on current phase
	switch iamCred.Status.Phase {
	case iamv1.IAMCredentialPhasePending:
		return r.handlePendingPhase(ctx, &iamCred)
	case iamv1.IAMCredentialPhaseValidating:
		return r.handleValidatingPhase(ctx, &iamCred)
	case iamv1.IAMCredentialPhaseValid:
		return r.handleValidPhase(ctx, &iamCred)
	case iamv1.IAMCredentialPhaseInvalid, iamv1.IAMCredentialPhaseFailed:
		return r.handleFailedPhase(ctx, &iamCred)
	default:
		logger.Info("Unknown phase, resetting to Pending", "phase", iamCred.Status.Phase)
		iamCred.Status.Phase = iamv1.IAMCredentialPhasePending
		return ctrl.Result{Requeue: true}, r.updateStatus(ctx, &iamCred)
	}
}

// SetupWithManager sets up the controller with the Manager.
func (r *IAMCredentialReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&iamv1.IAMCredential{}).
		Named("iam-iamcredential").
		Complete(r)
}
