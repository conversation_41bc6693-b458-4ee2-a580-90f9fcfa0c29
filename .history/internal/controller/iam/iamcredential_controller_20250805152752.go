/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package iam

import (
	"context"
	"fmt"
	"time"

	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/log"

	iamv1 "github.com/abc-stack/iam-operator/api/iam/v1"
	"github.com/abc-stack/iam-operator/internal/config"
	"github.com/abc-stack/iam-operator/internal/discovery"
	"github.com/abc-stack/iam-operator/internal/processors"
)

// IAMCredentialReconciler reconciles a IAMCredential object
type IAMCredentialReconciler struct {
	client.Client
	Scheme *runtime.Scheme

	// Dependencies
	Config              *config.Config
	ServiceDiscovery    *discovery.ServiceDiscovery
	CredentialProcessor *processors.CredentialProcessor
	KubernetesClient    kubernetes.Interface
}

// +kubebuilder:rbac:groups=iam.abcstackint.com,resources=iamcredentials,verbs=get;list;watch;create;update;patch;delete
// +kubebuilder:rbac:groups=iam.abcstackint.com,resources=iamcredentials/status,verbs=get;update;patch
// +kubebuilder:rbac:groups=iam.abcstackint.com,resources=iamcredentials/finalizers,verbs=update

// Reconcile is part of the main kubernetes reconciliation loop which aims to
// move the current state of the cluster closer to the desired state.
func (r *IAMCredentialReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
	logger := log.FromContext(ctx)

	// Fetch the IAMCredential instance
	var iamCred iamv1.IAMCredential
	if err := r.Get(ctx, req.NamespacedName, &iamCred); err != nil {
		if errors.IsNotFound(err) {
			// Request object not found, could have been deleted after reconcile request.
			logger.Info("IAMCredential resource not found. Ignoring since object must be deleted")
			return ctrl.Result{}, nil
		}
		// Error reading the object - requeue the request.
		logger.Error(err, "Failed to get IAMCredential")
		return ctrl.Result{}, err
	}

	logger.Info("Reconciling IAMCredential", "name", iamCred.Name, "namespace", iamCred.Namespace)

	// Update observed generation
	if iamCred.Status.ObservedGeneration != iamCred.Generation {
		iamCred.Status.ObservedGeneration = iamCred.Generation
	}

	// Set initial phase if not set
	if iamCred.Status.Phase == "" {
		iamCred.Status.Phase = iamv1.IAMCredentialPhasePending
		if err := r.updateStatus(ctx, &iamCred); err != nil {
			return ctrl.Result{}, err
		}
	}

	// Process the credential based on current phase
	switch iamCred.Status.Phase {
	case iamv1.IAMCredentialPhasePending:
		return r.handlePendingPhase(ctx, &iamCred)
	case iamv1.IAMCredentialPhaseValidating:
		return r.handleValidatingPhase(ctx, &iamCred)
	case iamv1.IAMCredentialPhaseValid:
		return r.handleValidPhase(ctx, &iamCred)
	case iamv1.IAMCredentialPhaseInvalid, iamv1.IAMCredentialPhaseFailed:
		return r.handleFailedPhase(ctx, &iamCred)
	default:
		logger.Info("Unknown phase, resetting to Pending", "phase", iamCred.Status.Phase)
		iamCred.Status.Phase = iamv1.IAMCredentialPhasePending
		return ctrl.Result{Requeue: true}, r.updateStatus(ctx, &iamCred)
	}
}

// updateStatus updates the status of the IAMCredential resource
func (r *IAMCredentialReconciler) updateStatus(ctx context.Context, iamCred *iamv1.IAMCredential) error {
	return r.Status().Update(ctx, iamCred)
}

// handlePendingPhase handles the Pending phase
func (r *IAMCredentialReconciler) handlePendingPhase(ctx context.Context, iamCred *iamv1.IAMCredential) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	logger.Info("Handling Pending phase", "name", iamCred.Name)

	// Transition to Validating phase
	iamCred.Status.Phase = iamv1.IAMCredentialPhaseValidating
	if err := r.updateStatus(ctx, iamCred); err != nil {
		return ctrl.Result{}, err
	}

	// Requeue immediately to start validation
	return ctrl.Result{Requeue: true}, nil
}

// handleValidatingPhase handles the Validating phase
func (r *IAMCredentialReconciler) handleValidatingPhase(ctx context.Context, iamCred *iamv1.IAMCredential) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	logger.Info("Handling Validating phase", "name", iamCred.Name)

	// Extract credentials from the specified source
	credentials, err := r.extractCredentialsFromSource(ctx, iamCred)
	if err != nil {
		return r.transitionToFailed(ctx, iamCred, fmt.Sprintf("Failed to extract credentials: %v", err))
	}

	if len(credentials) == 0 {
		return r.transitionToFailed(ctx, iamCred, "No credentials found in source")
	}

	// Validate credentials using the existing processor
	validationSuccess := true
	var validationError error
	var endpoint string

	// Get IAM endpoint
	if r.ServiceDiscovery != nil {
		endpoint, err = r.ServiceDiscovery.GetIAMEndpoint(ctx)
		if err != nil {
			logger.Error(err, "Failed to discover IAM endpoint")
			endpoint = "unknown"
		}
	}

	// Process each credential
	for productName, cred := range credentials {
		if err := r.CredentialProcessor.ProcessCredentials(ctx, "IAMCredential", iamCred.Name, map[string]string{
			"access_key": cred.AccessKey,
			"secret_key": cred.SecretKey,
		}); err != nil {
			logger.Error(err, "Credential validation failed", "product", productName)
			validationSuccess = false
			validationError = err
			break
		}
	}

	// Update status based on validation result
	now := metav1.Now()
	iamCred.Status.LastValidated = &now

	if validationSuccess {
		return r.transitionToValid(ctx, iamCred, endpoint)
	} else {
		return r.transitionToInvalid(ctx, iamCred, fmt.Sprintf("Validation failed: %v", validationError))
	}
}

// handleValidPhase handles the Valid phase
func (r *IAMCredentialReconciler) handleValidPhase(ctx context.Context, iamCred *iamv1.IAMCredential) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	logger.Info("Handling Valid phase", "name", iamCred.Name)

	// Check if we need to revalidate based on schedule
	if iamCred.Spec.Schedule != nil && *iamCred.Spec.Schedule != "" {
		// For now, implement a simple time-based revalidation
		// In a full implementation, you would parse the schedule and calculate next run time
		if iamCred.Status.LastValidated != nil {
			// Revalidate every 5 minutes as a simple example
			if time.Since(iamCred.Status.LastValidated.Time) > 5*time.Minute {
				logger.Info("Scheduled revalidation triggered")
				iamCred.Status.Phase = iamv1.IAMCredentialPhaseValidating
				if err := r.updateStatus(ctx, iamCred); err != nil {
					return ctrl.Result{}, err
				}
				return ctrl.Result{Requeue: true}, nil
			}
		}
	}

	// Requeue after a reasonable interval to check for schedule
	return ctrl.Result{RequeueAfter: 1 * time.Minute}, nil
}

// handleFailedPhase handles the Invalid and Failed phases
func (r *IAMCredentialReconciler) handleFailedPhase(ctx context.Context, iamCred *iamv1.IAMCredential) (ctrl.Result, error) {
	logger := log.FromContext(ctx)
	logger.Info("Handling Failed/Invalid phase", "name", iamCred.Name, "phase", iamCred.Status.Phase)

	// For failed credentials, we can implement retry logic here
	// For now, just requeue after a longer interval
	return ctrl.Result{RequeueAfter: 5 * time.Minute}, nil
}

// extractCredentialsFromSource extracts credentials from the specified source
func (r *IAMCredentialReconciler) extractCredentialsFromSource(ctx context.Context, iamCred *iamv1.IAMCredential) (map[string]*processors.Credential, error) {
	logger := log.FromContext(ctx)
	credentials := make(map[string]*processors.Credential)

	source := iamCred.Spec.Source

	// Handle ConfigMap source
	if source.ConfigMapRef != nil {
		configMapData, err := r.getConfigMapData(ctx, source.ConfigMapRef, iamCred.Namespace)
		if err != nil {
			return nil, fmt.Errorf("failed to get ConfigMap data: %w", err)
		}

		// Use the existing credential processor to extract credentials
		extractedCreds := r.CredentialProcessor.ExtractCredentials(configMapData)
		for product, cred := range extractedCreds {
			credentials[product] = cred
		}
	}

	// Handle Secret source
	if source.SecretRef != nil {
		secretData, err := r.getSecretData(ctx, source.SecretRef, iamCred.Namespace)
		if err != nil {
			return nil, fmt.Errorf("failed to get Secret data: %w", err)
		}

		// Convert secret data to string map and extract credentials
		stringData := make(map[string]string)
		for key, value := range secretData {
			stringData[key] = string(value)
		}

		extractedCreds := r.CredentialProcessor.ExtractCredentials(stringData)
		for product, cred := range extractedCreds {
			credentials[product] = cred
		}
	}

	// Handle inline source
	if source.Inline != nil {
		credentials["inline"] = &processors.Credential{
			AccessKey: source.Inline.AccessKey,
			SecretKey: source.Inline.SecretKey,
			Product:   "inline",
		}
	}

	logger.Info("Extracted credentials", "count", len(credentials))
	return credentials, nil
}

// getConfigMapData retrieves data from a ConfigMap
func (r *IAMCredentialReconciler) getConfigMapData(ctx context.Context, ref *iamv1.ConfigMapReference, defaultNamespace string) (map[string]string, error) {
	namespace := ref.Namespace
	if namespace == "" {
		namespace = defaultNamespace
	}

	var configMap corev1.ConfigMap
	if err := r.Get(ctx, types.NamespacedName{
		Name:      ref.Name,
		Namespace: namespace,
	}, &configMap); err != nil {
		return nil, err
	}

	// If a specific key is requested, return only that key
	if ref.Key != "" {
		if value, exists := configMap.Data[ref.Key]; exists {
			return map[string]string{ref.Key: value}, nil
		}
		return nil, fmt.Errorf("key %s not found in ConfigMap", ref.Key)
	}

	return configMap.Data, nil
}

// getSecretData retrieves data from a Secret
func (r *IAMCredentialReconciler) getSecretData(ctx context.Context, ref *iamv1.SecretReference, defaultNamespace string) (map[string][]byte, error) {
	namespace := ref.Namespace
	if namespace == "" {
		namespace = defaultNamespace
	}

	var secret corev1.Secret
	if err := r.Get(ctx, types.NamespacedName{
		Name:      ref.Name,
		Namespace: namespace,
	}, &secret); err != nil {
		return nil, err
	}

	// If a specific key is requested, return only that key
	if ref.Key != "" {
		if value, exists := secret.Data[ref.Key]; exists {
			return map[string][]byte{ref.Key: value}, nil
		}
		return nil, fmt.Errorf("key %s not found in Secret", ref.Key)
	}

	return secret.Data, nil
}

// SetupWithManager sets up the controller with the Manager.
func (r *IAMCredentialReconciler) SetupWithManager(mgr ctrl.Manager) error {
	return ctrl.NewControllerManagedBy(mgr).
		For(&iamv1.IAMCredential{}).
		Named("iam-iamcredential").
		Complete(r)
}
