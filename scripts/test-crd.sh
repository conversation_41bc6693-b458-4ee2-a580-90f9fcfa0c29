#!/bin/bash

# Test script for IAM Operator CRD mode
set -e

echo "=== Testing IAM Operator CRD Mode ==="

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    echo "Error: kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we're connected to a cluster
if ! kubectl cluster-info &> /dev/null; then
    echo "Error: Not connected to a Kubernetes cluster"
    exit 1
fi

echo "✓ Connected to Kubernetes cluster"

# Install CRDs
echo "Installing CRDs..."
kubectl apply -f config/crd/bases/

echo "✓ CRDs installed"

# Install RBAC
echo "Installing RBAC..."
kubectl apply -f config/rbac/

echo "✓ RBAC installed"

# Create test resources
echo "Creating test resources..."
kubectl apply -f examples/test-iam-credential.yaml

echo "✓ Test resources created"

# Wait a moment for resources to be created
sleep 2

# Check IAMCredential resources
echo ""
echo "=== IAMCredential Resources ==="
kubectl get iamcredentials -o wide

echo ""
echo "=== IAMCredential Status Details ==="
for cred in $(kubectl get iamcredentials -o name); do
    echo "--- $cred ---"
    kubectl describe $cred
    echo ""
done

echo ""
echo "=== ConfigMaps ==="
kubectl get configmaps test-config -o yaml

echo ""
echo "=== Secrets ==="
kubectl get secrets test-secret -o yaml

echo ""
echo "Test completed! Check the IAMCredential status above."
echo "To clean up, run: kubectl delete -f examples/test-iam-credential.yaml"
