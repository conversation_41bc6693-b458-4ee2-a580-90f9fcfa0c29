---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - secrets
  - services
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
- apiGroups:
  - iam.abcstackint.com
  resources:
  - iamcredentials
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - iam.abcstackint.com
  resources:
  - iamcredentials/finalizers
  verbs:
  - update
- apiGroups:
  - iam.abcstackint.com
  resources:
  - iamcredentials/status
  verbs:
  - get
  - patch
  - update
