# This rule is not used by the project iam-operator-go itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants read-only access to iam.abcstackint.com resources.
# This role is intended for users who need visibility into these resources
# without permissions to modify them. It is ideal for monitoring purposes and limited-access viewing.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
  name: iam-iamcredential-viewer-role
rules:
- apiGroups:
  - iam.abcstackint.com
  resources:
  - iamcredentials
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - iam.abcstackint.com
  resources:
  - iamcredentials/status
  verbs:
  - get
