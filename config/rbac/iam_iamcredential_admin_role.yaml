# This rule is not used by the project iam-operator-go itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants full permissions ('*') over iam.abcstackint.com.
# This role is intended for users authorized to modify roles and bindings within the cluster,
# enabling them to delegate specific permissions to other users or groups as needed.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
  name: iam-iamcredential-admin-role
rules:
- apiGroups:
  - iam.abcstackint.com
  resources:
  - iamcredentials
  verbs:
  - '*'
- apiGroups:
  - iam.abcstackint.com
  resources:
  - iamcredentials/status
  verbs:
  - get
