# This rule is not used by the project iam-operator-go itself.
# It is provided to allow the cluster admin to help manage permissions for users.
#
# Grants permissions to create, update, and delete resources within the iam.abcstackint.com.
# This role is intended for users who need to manage these resources
# but should not control RBAC or manage permissions for others.

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: iam-operator-go
    app.kubernetes.io/managed-by: kustomize
  name: iam-iamcredential-editor-role
rules:
- apiGroups:
  - iam.abcstackint.com
  resources:
  - iamcredentials
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - iam.abcstackint.com
  resources:
  - iamcredentials/status
  verbs:
  - get
