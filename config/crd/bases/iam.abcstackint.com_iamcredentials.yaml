---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.18.0
  name: iamcredentials.iam.abcstackint.com
spec:
  group: iam.abcstackint.com
  names:
    kind: IAMCredential
    listKind: IAMCredentialList
    plural: iamcredentials
    shortNames:
    - iamcred
    singular: iamcredential
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: Phase
      type: string
    - jsonPath: .status.lastValidated
      name: Last Validated
      type: date
    - jsonPath: .spec.source.configMapRef.name
      name: Source
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        description: IAMCredential is the Schema for the iamcredentials API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: spec defines the desired state of IAMCredential
            properties:
              schedule:
                description: Schedule defines periodic validation schedule (e.g.,
                  "*/5m" for every 5 minutes)
                type: string
              source:
                description: Source defines where to find the IAM credentials
                properties:
                  configMapRef:
                    description: ConfigMapRef references a ConfigMap containing credentials
                    properties:
                      key:
                        description: Key within the ConfigMap (optional, will scan
                          all keys if not specified)
                        type: string
                      name:
                        description: Name of the ConfigMap
                        type: string
                      namespace:
                        description: Namespace of the ConfigMap (defaults to the same
                          namespace as IAMCredential)
                        type: string
                    required:
                    - name
                    type: object
                  inline:
                    description: Inline credentials (for testing only, not recommended
                      for production)
                    properties:
                      accessKey:
                        description: AccessKey for IAM authentication
                        type: string
                      data:
                        additionalProperties:
                          type: string
                        description: Additional credential data
                        type: object
                      secretKey:
                        description: SecretKey for IAM authentication
                        type: string
                    required:
                    - accessKey
                    - secretKey
                    type: object
                  secretRef:
                    description: SecretRef references a Secret containing credentials
                    properties:
                      key:
                        description: Key within the Secret (optional, will scan all
                          keys if not specified)
                        type: string
                      name:
                        description: Name of the Secret
                        type: string
                      namespace:
                        description: Namespace of the Secret (defaults to the same
                          namespace as IAMCredential)
                        type: string
                    required:
                    - name
                    type: object
                type: object
              validation:
                description: Validation defines validation behavior
                properties:
                  enabled:
                    default: true
                    description: Enabled controls whether validation is performed
                    type: boolean
                  endpoint:
                    default: auto-discover
                    description: Endpoint specifies the IAM API endpoint ("auto-discover"
                      for automatic discovery)
                    type: string
                  timeout:
                    default: 30s
                    description: Timeout for validation requests
                    type: string
                type: object
            required:
            - source
            type: object
          status:
            description: status defines the observed state of IAMCredential
            properties:
              conditions:
                description: Conditions represent the latest available observations
                  of the IAMCredential's state
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              lastValidated:
                description: LastValidated timestamp of last successful validation
                format: date-time
                type: string
              observedGeneration:
                description: ObservedGeneration reflects the generation of the most
                  recently observed IAMCredential
                format: int64
                type: integer
              phase:
                description: Phase represents the current phase of the IAMCredential
                enum:
                - Pending
                - Validating
                - Valid
                - Invalid
                - Failed
                type: string
              validationResult:
                description: ValidationResult contains the result of the last validation
                properties:
                  accessKeyPrefix:
                    description: AccessKeyPrefix shows the first 8 characters of the
                      access key (for security)
                    type: string
                  endpoint:
                    description: Endpoint is the IAM API endpoint that was used for
                      validation
                    type: string
                  error:
                    description: Error contains error details if validation failed
                    type: string
                  message:
                    description: Message provides additional information about the
                      validation result
                    type: string
                  success:
                    description: Success indicates whether validation was successful
                    type: boolean
                  validationTime:
                    description: ValidationTime is when the validation was performed
                    format: date-time
                    type: string
                required:
                - success
                type: object
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
    subresources:
      status: {}
