# IAM Operator CRD Mode

## 概述

IAM Operator 的 CRD (Custom Resource Definition) 模式提供了一种声明式的方式来管理 IAM 凭据验证。与被动监听 ConfigMap 的模式不同，CRD 模式允许用户主动声明需要验证的凭据，提供更精确的控制和更好的可观测性。

## 特性

- **声明式管理**: 通过 Kubernetes 原生的 CRD 资源声明凭据验证需求
- **多源支持**: 支持从 ConfigMap、Secret 和内联方式获取凭据
- **状态跟踪**: 提供详细的验证状态和结果反馈
- **调度验证**: 支持定时重新验证凭据
- **生命周期管理**: 完整的凭据验证生命周期管理

## IAMCredential 资源

### API 版本
- **Group**: `iam.abcstackint.com`
- **Version**: `v1`
- **Kind**: `IAMCredential`

### 规格 (Spec)

```yaml
apiVersion: iam.abcstackint.com/v1
kind: IAMCredential
metadata:
  name: my-credential
  namespace: default
spec:
  source:
    # 三种凭据源之一
    configMapRef:
      name: my-config
      namespace: default  # 可选，默认为资源所在命名空间
      key: credentials    # 可选，指定特定键
    # 或者
    secretRef:
      name: my-secret
      namespace: default  # 可选
      key: credentials    # 可选
    # 或者
    inline:
      accessKey: "your-access-key"
      secretKey: "your-secret-key"
  
  schedule: "*/5 * * * *"  # 可选，定时验证调度
```

### 状态 (Status)

```yaml
status:
  phase: "Valid"  # Pending, Validating, Valid, Invalid, Failed
  observedGeneration: 1
  lastValidated: "2024-08-05T10:30:00Z"
  validationResult:
    success: true
    message: "Credential validation successful"
    endpoint: "https://iam.example.com"
    validationTime: "2024-08-05T10:30:00Z"
    error: ""  # 仅在失败时填充
  conditions:
    - type: "Ready"
      status: "True"
      reason: "ValidationSuccessful"
      message: "IAM credentials validated successfully"
      lastTransitionTime: "2024-08-05T10:30:00Z"
```

## 使用示例

### 1. 内联凭据

```yaml
apiVersion: iam.abcstackint.com/v1
kind: IAMCredential
metadata:
  name: inline-credential
  namespace: default
spec:
  source:
    inline:
      accessKey: "AKIAIOSFODNN7EXAMPLE"
      secretKey: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
  schedule: "*/10 * * * *"
```

### 2. ConfigMap 凭据

```yaml
# ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: iam-config
  namespace: default
data:
  credentials.yaml: |
    iam:
      bss:
        ak: "AKIAIOSFODNN7EXAMPLE"
        sk: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"

---
# IAMCredential
apiVersion: iam.abcstackint.com/v1
kind: IAMCredential
metadata:
  name: configmap-credential
  namespace: default
spec:
  source:
    configMapRef:
      name: iam-config
      key: credentials.yaml
```

### 3. Secret 凭据

```yaml
# Secret
apiVersion: v1
kind: Secret
metadata:
  name: iam-secret
  namespace: default
type: Opaque
data:
  access_key: QUtJQUlPU0ZPRE5ON0VYQU1QTEU=  # base64 encoded
  secret_key: d0phbHJYVXRuRkVNSS9LN01ERU5HL2JQeFJmaUNZRVhBTVBMRUtFWQ==

---
# IAMCredential
apiVersion: iam.abcstackint.com/v1
kind: IAMCredential
metadata:
  name: secret-credential
  namespace: default
spec:
  source:
    secretRef:
      name: iam-secret
```

## 支持的凭据格式

IAM Operator 支持多种凭据格式：

### 1. 直接键值对
```yaml
access_key: "your-access-key"
secret_key: "your-secret-key"
```

### 2. PHP 配置格式
```php
$bss_ak = "your-access-key";
$bss_sk = "your-secret-key";
```

### 3. YAML 嵌套格式
```yaml
iam:
  bss:
    ak: "your-access-key"
    sk: "your-secret-key"
```

### 4. 配置文件格式
```
bss_ak=your-access-key
bss_sk=your-secret-key
```

## 状态阶段

- **Pending**: 资源已创建，等待处理
- **Validating**: 正在验证凭据
- **Valid**: 凭据验证成功
- **Invalid**: 凭据验证失败
- **Failed**: 处理过程中发生错误

## 部署和测试

### 1. 安装 CRD
```bash
kubectl apply -f config/crd/bases/
```

### 2. 安装 RBAC
```bash
kubectl apply -f config/rbac/
```

### 3. 部署控制器
```bash
kubectl apply -f config/manager/
```

### 4. 创建测试资源
```bash
kubectl apply -f examples/test-iam-credential.yaml
```

### 5. 检查状态
```bash
# 查看所有 IAMCredential 资源
kubectl get iamcredentials

# 查看详细状态
kubectl describe iamcredential my-credential

# 查看控制器日志
kubectl logs -n iam-operator-system deployment/iam-operator-controller-manager
```

## 故障排除

### 常见问题

1. **凭据验证失败**
   - 检查凭据是否正确
   - 确认 IAM 服务端点可访问
   - 查看 IAMCredential 状态中的错误信息

2. **资源无法创建**
   - 确认 CRD 已正确安装
   - 检查 RBAC 权限
   - 验证资源定义语法

3. **控制器无法访问 ConfigMap/Secret**
   - 检查 RBAC 权限
   - 确认资源存在于正确的命名空间
   - 验证引用的键名正确

### 调试命令

```bash
# 查看 CRD 定义
kubectl get crd iamcredentials.iam.abcstackint.com -o yaml

# 查看控制器状态
kubectl get pods -n iam-operator-system

# 查看控制器日志
kubectl logs -n iam-operator-system -l control-plane=controller-manager

# 查看事件
kubectl get events --sort-by=.metadata.creationTimestamp
```

## 与 ConfigMap 模式的对比

| 特性 | ConfigMap 模式 | CRD 模式 |
|------|----------------|----------|
| 管理方式 | 被动监听 | 主动声明 |
| 精确控制 | 有限 | 完全控制 |
| 状态反馈 | 日志 | 资源状态 |
| 调度验证 | 固定间隔 | 可配置 |
| 多源支持 | ConfigMap 只 | ConfigMap/Secret/内联 |
| Kubernetes 原生 | 部分 | 完全 |

CRD 模式提供了更好的用户体验和更强的功能，推荐在生产环境中使用。
