apiVersion: iam.abcstackint.com/v1
kind: IAMCredential
metadata:
  name: test-credential
  namespace: default
spec:
  source:
    inline:
      accessKey: "test-access-key"
      secretKey: "test-secret-key"
  schedule: "*/5 * * * *"  # Validate every 5 minutes
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: test-config
  namespace: default
data:
  credentials.yaml: |
    iam:
      bss:
        ak: "configmap-access-key"
        sk: "configmap-secret-key"
---
apiVersion: iam.abcstackint.com/v1
kind: IAMCredential
metadata:
  name: configmap-credential
  namespace: default
spec:
  source:
    configMapRef:
      name: test-config
      key: credentials.yaml
  schedule: "*/10 * * * *"  # Validate every 10 minutes
---
apiVersion: v1
kind: Secret
metadata:
  name: test-secret
  namespace: default
type: Opaque
data:
  access_key: c2VjcmV0LWFjY2Vzcy1rZXk=  # base64 encoded "secret-access-key"
  secret_key: c2VjcmV0LXNlY3JldC1rZXk=  # base64 encoded "secret-secret-key"
---
apiVersion: iam.abcstackint.com/v1
kind: IAMCredential
metadata:
  name: secret-credential
  namespace: default
spec:
  source:
    secretRef:
      name: test-secret
