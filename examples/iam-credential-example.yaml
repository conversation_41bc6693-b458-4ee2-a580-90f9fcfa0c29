apiVersion: iam.abcstackint.com/v1
kind: IAMCredential
metadata:
  name: console-app-credentials
  namespace: default
spec:
  source:
    configMapRef:
      name: console-config
      namespace: console
  validation:
    enabled: true
    endpoint: "auto-discover"
    timeout: "30s"
  schedule: "*/5m"
---
apiVersion: iam.abcstackint.com/v1
kind: IAMCredential
metadata:
  name: test-secret-credentials
  namespace: default
spec:
  source:
    secretRef:
      name: test-secret
      namespace: default
      key: "credentials"
  validation:
    enabled: true
    endpoint: "auto-discover"
---
apiVersion: iam.abcstackint.com/v1
kind: IAMCredential
metadata:
  name: inline-test-credentials
  namespace: default
spec:
  source:
    inline:
      accessKey: "test-access-key"
      secretKey: "test-secret-key"
      data:
        region: "us-west-2"
        project: "test-project"
  validation:
    enabled: true
    endpoint: "http://100.69.244.105:8468/v3"
